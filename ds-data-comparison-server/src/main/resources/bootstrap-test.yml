server:
  port: 39100
  servlet:
    context-path: /ds-data-comparison-server

spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    url: ********************************************************************************************************************
    username: root
    password: Mysql#r2368
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 # 最小空闲连接数
      max-active: 20 # 最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"     # 不统计这些请求数据
    hikari:
      read-only:
      maximum-pool-size: 100
      connection-timeout: 15000
  servlet:
    multipart:
      max-request-size: 550MB
      max-file-size: 550MB
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.163.com
    # 发送者邮箱
    username: <EMAIL>
    # 配置密码，注意不是真正的密码，而是刚刚申请到的授权码
    password: IFTQSZOTVFOFRTIX
    # 端口号465或587或25
    port: 25
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
        #表示开启 DEBUG 模式，这样，邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: true
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${upload.filePathPrefix}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  freemarker:
    cache: false  #关闭模板缓存，方便测试
    settings:
      template_update_delay: 0 #检查模板更新延迟时间，设置为0表示立即检查，如果时间大于0会有缓存不方便进行模板测试
    suffix: .ftl               #指定Freemarker模板文件的后缀名
  data:
    mongodb:
      database: ds
      uri: *********************************************************************************************
      socket-timeout: 60000        # socket超时时间
      connect-timeout: 30000       # 连接超时时间
      server-selection-timeout: 30000  # 服务器选择超时时间
  rabbitmq:
    host: ***********
#    host: **************
    port: 5672
    username: admin
    password: admin368
    virtual-host: /
    listener:
      direct:
        prefetch: 3
      simple:
        acknowledge-mode: manual
management:
  health:
    mail:
      enabled: false
redisson:
  enable: true
  mode: single
  address: redis://***********:6379
  connectTimeout: 3000
  idleConnectionTimeout: 10000
  timeout: 10000
  password: D1zBa9CsY43d0
  clientName: ${spring.application.name}
  database: 7
  connectionPoolSize: 100
  connectionMinimumIdleSize: 50
  masterConnectionMinimumIdleSize: 1
  masterConnectionPoolSize: 64

logging:
  level:
    com.r2: debug
  config: classpath:logback-spring.xml
  path: /data/logs/${spring.application.name}/

shiro:
  sessionTimeOut: 604800000

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

upload:
  filePathPrefix: /home/<USER>/uploadFile/ds/data-comparison/
  tempFilePathPrefix: /tmp/ds/data-comparison/

