package com.r2.ds.dsDataComparison.config.shiro;

import org.apache.shiro.authc.UsernamePasswordToken;

public class NoPwdToken extends UsernamePasswordToken {

    public NoPwdToken(String username) {
        this.username = username;
    }

    private String username;

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public void setUsername(String username) {
        this.username = username;
    }
}
