package com.r2.ds.dsDataComparison.config;

import com.r2.ds.dsDataComparison.config.shiro.NoPwdToken;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authc.credential.SimpleCredentialsMatcher;
import org.apache.shiro.crypto.hash.Md5Hash;

import java.nio.charset.StandardCharsets;

public class CredentialsMatcher extends SimpleCredentialsMatcher {

    /**
     * 密码验证
     * @param realPassword
     * @param salt
     * @param checkPassword
     * @return
     */
    public static boolean checkPassword(String realPassword, String salt, String checkPassword) {
        String password = generatePassword(salt, checkPassword);
        return password.equals(realPassword);
    }

    /**
     * 根据salt和原始密码生成密码
     * @param salt
     * @param originPassword
     * @return
     */
    public static String generatePassword(String salt, String originPassword) {
        return new Md5Hash(originPassword, new String(salt.getBytes(), StandardCharsets.UTF_8), 2).toString();
    }
    /**
     * 校验密码
     * @param authToken
     * @param info
     * @return
     */
    @Override
    public boolean doCredentialsMatch(AuthenticationToken authToken, AuthenticationInfo info) {
        //ad域登录，不校验密码
        if (authToken instanceof NoPwdToken) {
            return true;
        }

        UsernamePasswordToken token = (UsernamePasswordToken) authToken;
        SimpleAuthenticationInfo simpleAuthenticationInfo = (SimpleAuthenticationInfo) info;
        String cypheredPassword = new Md5Hash(token.getPassword(),
                new String(simpleAuthenticationInfo.getCredentialsSalt().getBytes(), StandardCharsets.UTF_8), 2).toString();
        return this.equals(cypheredPassword, getCredentials(info));
    }

}
