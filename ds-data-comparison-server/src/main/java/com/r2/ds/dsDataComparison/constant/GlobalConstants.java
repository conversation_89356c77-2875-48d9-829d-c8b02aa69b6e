package com.r2.ds.dsDataComparison.constant;

public interface GlobalConstants {

    /**
     * realm_name
     */
    String REALM_NAME = "managerRealm";
    /**
     * shiro权限缓存
     */
    String SHIRO_AUTHORIZATION_CACHE = "shiro-authorization";

    /**
     * 默认时间
     */
    String DEFAULT_TIME = "1970-01-01 00:00:00";

    /**
     * 语言类型
     */
    String LANG = "lang";

    /**
     * 初始
     */
    String INIT = "_init";

    /**
     * 开发，测试环境万能图片验证码
     */
    String DEFAULT_IMG_CODE = "1111";

    /**
     * 全部 中文
     */
    String ALL_OPTION_CN = "全部";

    /**
     * 全部 英文
     */
    String ALL_OPTION_EN = "all";

    /**
     * 前缀
     */
    String ID_PREFIX = "index_";

    /**
     * 对比标识
     */
    String CONTRAST_FLAG = "contrastFlag";

    /**
     * 更新字段
     */
    String UPDATE_FIELDS = "updateFields";
    /**
     * utf-8
     */
    String UTF8 = "UTF-8";
    /**
     * 导出excel 文件后缀
     */
    String EXPORT_EXCEL_SUFFIX = ".xlsx";

    /**
     * test-xiaoshan
     */
    String TEST = "test";

    /**
     * 对比导出路径
     */
    String EXTRACT_EXPORT_FILE = "extractExportFile";

    interface ReturnFormat {
        /**
         * 字符串
         */
        String STRING = "1";
        /**
         * 日期
         */
        String DATE = "2";
        /**
         * 数字
         */
        String NUMBER = "3";
    }

    interface Calculation {
        /**
         * 表达式
         */
        String FORMULA = "0";
        /**
         * 聚合函数
         */
        String AGGREGATE = "1";
        /**
         * 自定义函数
         */
        String FUNCTION = "2";
        /**
         * 数据规整
         */
        String DATA_NORMALIZATION = "3";
    }

    interface AggregationType {
        /**
         * sum
         */
        Integer SUM = 0;
        /**
         * count
         */
        Integer COUNT = 1;
        /**
         * avg
         */
        Integer AVG = 2;
        /**
         * min
         */
        Integer MIN = 3;
        /**
         * max
         */
        Integer MAX = 4;
        /**
         * 时间排序取最早
         */
        Integer EARLIEST = 5;
        /**
         * 时间排序取最晚
         */
        Integer LATEST = 6;
    }

    interface QueryBuilderType {
        /**
         * 字段
         */
        String FIELD = "field";
        /**
         * 值
         */
        String VALUE = "value";
    }

    /**
     * FormOID
     */
    String FORM_OID = "FormOID";

    /**
     * FieldOID
     */
    String FIELD_OID = "FieldOID";

    /**
     * SASLabel
     */
    String SAS_LABEL = "SASLabel";


    /**
     * 最大字符长度
     */
    Integer MAX_CHAR_LENGTH = 255;

    /**
     * 四种颜色
     */
    String COLOR1 = "E6E6EB";
    String COLOR2 = "E5F0F9";
    String COLOR3 = "FEF7DB";
    String COLOR4 = "EAF4EA";

    /**
     * 民族
     */
    String NATION = "Nation";

    /**
     * 民族
     */
    String NATION_CN = "民族";

    /**
     * 页面名称
     */
    String FORMNM = "FORMNM";

    /**
     * FIELDS
     */
    String FIELDS = "Fields";

    /**
     * Forms
     */
    String FORMS = "Forms";

    /**
     * OID
     */
    String OID = "OID";

    /**
     * DraftFormName
     */
    String DRAFT_FORM_NAME = "DraftFormName";

    /**
     * value
     */
    String VALUE = "value";
    /**
     * 对比类型
     */
    String CONTRAST_TYPE = "contrastType";
    /**
     * 字段范围
     */
    String FIELD_RANGE = "fieldRange";
    /**
     * 自动问询
     */
    String AUTO_TEXT = "autoText";
    /**
     * 手动问询
     */
    String HAND_TEXT = "handText";

    /**
     * 映射问询标签名
     */
    String CONSULT_TITLE = "consultTitle";

    /**
     * 数据状态
     */
    String DATA_STATUS = "数据状态";
    /**
     * 审阅状态
     */
    String REVIEW_STATUS = "审阅状态";

    /**
     * 审阅文件
     */
    String DATA_REVIEW_FILE = "dataReviewFile";

    /**
     * 问询
     */
    String CONSULT = "问询";

    /**
     * 手动问询
     */
    String HAND_CONSULT = "手动问询";

    /**
     * 无sheetID
     */
    String NO_SHEET_ID = "-1";

    /**
     * CDASH
     */
    String CDASH = "CDASH";

}
