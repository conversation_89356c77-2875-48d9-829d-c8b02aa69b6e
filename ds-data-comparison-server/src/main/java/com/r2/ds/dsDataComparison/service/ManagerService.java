package com.r2.ds.dsDataComparison.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.ds.dsDataComparison.entity.hub.Manager;

/**
 * 人员表(Manager)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-04 10:21:55
 */
public interface ManagerService extends IService<Manager> {


    /**
     * 查询用户
     * @param userName
     * @return
     */
    Manager getInfoByUserName(String userName);

}

