package com.r2.ds.dsDataComparison.resolver;

import com.r2.ds.dsDataComparison.enums.LangTypeEnum;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

public class MyLocalResolver implements LocaleResolver {

    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        String language = request.getHeader("lang");
        Locale locale = new Locale("zh", "CN");;
        if (Strings.isNotBlank(language)) {
            if (language.equals(LangTypeEnum.EN.getLang())) {
                locale = new Locale("en", "US");
            }
        }
        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
    }
}
