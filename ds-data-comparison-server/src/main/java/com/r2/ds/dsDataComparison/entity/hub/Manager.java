package com.r2.ds.dsDataComparison.entity.hub;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 人员表(Manager)表实体类
 *
 * <AUTHOR>
 * @since 2024-03-04 10:21:55
 */
@SuppressWarnings("serial")
@TableName("t_manager")
@Data
public class Manager extends Model<Manager> {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
    *用户名
    */
    private String userName;
    /**
    *密码
    */
    private String password;
    /**
    *盐
    */
    private String salt;
    /**
    *管理员邮箱
    */
    private String email;
    /**
    *是否删除 1删除 0 未删除
    */
    private Integer isDelete;
    /**
    *创建时间
    */
    private Date createTime;
    /**
    *更新时间
    */
    private Date updateTime;
    /**
    *工号
    */
    private String jobNumber;

    /**
     *用户来源 1: 内部用户 2: 外部用户
     */
    private Integer sourceType;

    /**
     * 密码重置日期
     */
    private String resetDay;

    /**
     * 直属领导姓名
     */
    private String poiDempAdminName;

    /**
     * 职位
     */
    private String dutyName;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 用户账户
     */
    private String userAccount;

    /**
     * 部门
     */
    private String organizeCode;

    /**
     * 搜索
     */
    private String searchKey;

    /**
     * 所有数据权限：1：是；0：否
     */
    private Integer dataPermission;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
    }

