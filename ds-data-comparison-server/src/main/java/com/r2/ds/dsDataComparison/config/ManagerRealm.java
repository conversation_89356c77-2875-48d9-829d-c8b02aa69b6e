package com.r2.ds.dsDataComparison.config;


import com.r2.ds.dsDataComparison.config.shiro.NoPwdToken;
import com.r2.ds.dsDataComparison.constant.GlobalConstants;
import com.r2.ds.dsDataComparison.entity.hub.Manager;
import com.r2.ds.dsDataComparison.service.ManagerService;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ManagerRealm extends AuthorizingRealm {

    private ManagerService managerService;

    ManagerRealm(ManagerService managerService, String authorizationCacheName) {
        this.managerService = managerService;
        super.setAuthorizationCacheName(authorizationCacheName);
    }

    /**
     * realm的名称
     *
     * @return
     */
    @Override
    public String getName() {
        return GlobalConstants.REALM_NAME;
    }


    /**
     * 权限信息缓存的key
     *
     * @param principals
     * @return
     */
    @Override
    protected Object getAuthorizationCacheKey(PrincipalCollection principals) {
        return ((Manager) principals.getPrimaryPrincipal()).getId();
    }
    /**
     * 授权
     * @param principalCollection
     * @return
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        Manager manager = (Manager) principalCollection.getPrimaryPrincipal();
        log.info("doGetAuthorizationInfo, manager is {}", manager.getId());
        List<String> permissions = new ArrayList<>();
        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
        simpleAuthorizationInfo.addStringPermissions(permissions);
        return simpleAuthorizationInfo;
    }

    /**
     *
     * @param authenticationToken
     * @return
     * @throws AuthenticationException
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authenticationToken;
        String username = token.getUsername();
        log.info("doGetAuthorizationInfo, username is {}", username);
        Manager manager = managerService.getInfoByUserName(username);
        if (manager == null) {
            log.error("未查询到此用户 username={}", username);
            throw new CodeException(ResultCode.Codes.ERROR, "用户名或者密码错误");
        }
        //主数据平台登录
        if (authenticationToken instanceof NoPwdToken) {
            return new SimpleAuthenticationInfo(manager, "", this.getClass().getSimpleName());
        } else {
            return new SimpleAuthenticationInfo(manager, manager.getPassword(),
                    ByteSource.Util.bytes(manager.getSalt()), getName());
        }
    }

    /**
     * 清空缓存
     */
    public void clearCachedAuthorizationInfo() {
        this.clearCachedAuthorizationInfo(SecurityUtils.getSubject().getPrincipals());
    }
}
