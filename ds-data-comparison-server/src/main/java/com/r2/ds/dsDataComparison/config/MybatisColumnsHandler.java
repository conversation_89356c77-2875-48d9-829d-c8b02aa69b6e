package com.r2.ds.dsDataComparison.config;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.r2.ds.dsDataComparison.entity.hub.Manager;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class MybatisColumnsHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        Manager sysUser = (Manager) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            this.strictInsertFill(metaObject, "creatorId", Integer.class, sysUser.getId());
            this.strictInsertFill(metaObject, "updaterId", Integer.class, sysUser.getId());
        }
        Date date = new Date();
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject != null) {
            JSONObject json = new JSONObject(originalObject);
            if (json.get("createTime") == null) {
                this.strictInsertFill(metaObject, "createTime", Date.class, date);
            }
            if (json.get("updateTime") == null) {
                this.strictInsertFill(metaObject, "updateTime", Date.class, date);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Manager sysUser = (Manager) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            this.strictUpdateFill(metaObject, "updaterId", Integer.class, sysUser.getId());
        }
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject != null) {
            JSONObject json = new JSONObject(originalObject);
            if (json.get("updateTime") == null) {
                this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
            }
        }
    }
}
