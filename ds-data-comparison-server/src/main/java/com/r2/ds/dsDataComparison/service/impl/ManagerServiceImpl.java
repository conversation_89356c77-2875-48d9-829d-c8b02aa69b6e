package com.r2.ds.dsDataComparison.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.ds.dsDataComparison.dao.hub.ManagerDao;
import com.r2.ds.dsDataComparison.entity.hub.Manager;
import com.r2.ds.dsDataComparison.service.ManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (Manager)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-05 10:48:10
 */
@Slf4j
@Service("managerService")
public class ManagerServiceImpl extends ServiceImpl<ManagerDao, Manager> implements ManagerService {

    @Resource
    private ManagerDao managerDao;


    @Override
    public Manager getInfoByUserName(String userName) {
        QueryWrapper<Manager> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .and(wrapper -> wrapper.eq(Manager::getJob<PERSON><PERSON>ber, userName))
                .eq(Manager :: getIsDelete, false);
        return managerDao.selectOne(queryWrapper);
    }


}

